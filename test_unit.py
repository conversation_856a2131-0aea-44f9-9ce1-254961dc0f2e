#!/usr/bin/env python3
"""
Unit tests only - Frontend and Backend unit tests
"""
import os
import sys
import subprocess
import json
import datetime
from pathlib import Path
import shutil


class UnitTestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results_dir = self.project_root / 'test-results'
        self.test_results_dir.mkdir(exist_ok=True)
        
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'frontend': {},
            'backend': {},
            'summary': {}
        }
    
    def check_npm_available(self):
        """Check if npm is available"""
        npm_commands = ['npm', 'npm.cmd']
        for npm_cmd in npm_commands:
            if shutil.which(npm_cmd):
                return npm_cmd
        return None
    
    def run_frontend_tests(self):
        """Run frontend unit tests with Vitest"""
        print("🧪 Running Frontend Unit Tests...")
        print("=" * 50)
        
        npm_cmd = self.check_npm_available()
        if not npm_cmd:
            print("❌ npm not found. Please ensure Node.js and npm are installed.")
            self.results['frontend'] = {'status': 'error', 'error': 'npm not found'}
            return
        
        try:
            result = subprocess.run([
                npm_cmd, 'run', 'test:run'
            ], 
            cwd=str(self.project_root),
            capture_output=True, 
            text=True,
            timeout=120,
            shell=True,
            encoding='utf-8',
            errors='replace'
            )
            
            self.results['frontend'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            print(f"✅ Frontend tests completed with return code: {result.returncode}")
            
            # Parse test results for better display
            if result.stdout:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Test Files' in line:
                        print(f"📊 {line.strip()}")
                    elif 'Tests' in line and ('passed' in line or 'failed' in line):
                        print(f"📊 {line.strip()}")
            
        except Exception as e:
            print(f"❌ Error running frontend tests: {e}")
            self.results['frontend'] = {'status': 'error', 'error': str(e)}
    
    def run_backend_tests(self):
        """Run Django backend unit tests"""
        print("\n🧪 Running Backend Unit Tests...")
        print("=" * 50)
        
        try:
            backend_dir = self.project_root / 'backend'
            
            result = subprocess.run([
                sys.executable, 'manage.py', 'test', 
                '--verbosity=2',
                '--keepdb'
            ], 
            cwd=str(backend_dir),
            capture_output=True, 
            text=True,
            timeout=120,
            shell=True,
            encoding='utf-8',
            errors='replace'
            )
            
            self.results['backend'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            print(f"✅ Backend tests completed with return code: {result.returncode}")
            
            # Parse Django test output for better display
            if result.stdout:
                lines = result.stdout.split('\n')
                test_count = 0
                failures = 0
                errors = 0
                
                for line in lines:
                    if 'Found' in line and 'test(s)' in line:
                        test_count = line.strip()
                        print(f"📊 {test_count}")
                    elif 'FAILED' in line and 'failures' in line:
                        failures_line = line.strip()
                        print(f"📊 {failures_line}")
                    elif 'Ran' in line and 'tests' in line:
                        print(f"📊 {line.strip()}")
                    elif line.startswith('OK') or 'test' in line.lower() and ('passed' in line.lower() or 'ok' in line.lower()):
                        print(f"📊 {line.strip()}")
            
        except Exception as e:
            print(f"❌ Error running backend tests: {e}")
            self.results['backend'] = {'status': 'error', 'error': str(e)}
    
    def generate_summary(self):
        """Generate test summary"""
        frontend_status = self.results['frontend'].get('status', 'unknown')
        backend_status = self.results['backend'].get('status', 'unknown')
        
        overall_status = 'passed'
        if frontend_status in ['failed', 'error'] or backend_status in ['failed', 'error']:
            overall_status = 'failed'
        
        self.results['summary'] = {
            'overall_status': overall_status,
            'frontend_status': frontend_status,
            'backend_status': backend_status,
            'timestamp': self.results['timestamp']
        }
    
    def save_results(self):
        """Save test results"""
        results_file = self.test_results_dir / f'unit-test-report-{self.timestamp}.json'
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        latest_file = self.test_results_dir / 'latest-unit-test-report.json'
        with open(latest_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Unit test results saved to: {results_file}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("🎯 UNIT TEST SUMMARY")
        print("=" * 60)
        
        summary = self.results['summary']
        
        status_emoji = "✅" if summary['overall_status'] == 'passed' else "❌"
        print(f"{status_emoji} Overall Status: {summary['overall_status'].upper()}")
        
        frontend_emoji = "✅" if summary['frontend_status'] == 'passed' else "❌"
        print(f"{frontend_emoji} Frontend Unit Tests: {summary['frontend_status'].upper()}")
        
        backend_emoji = "✅" if summary['backend_status'] == 'passed' else "❌"
        print(f"{backend_emoji} Backend Unit Tests: {summary['backend_status'].upper()}")
        
        print(f"🕐 Timestamp: {summary['timestamp']}")
        print("=" * 60)
    
    def run_all_tests(self):
        """Run all unit tests"""
        print("🚀 Unit Test Suite (Frontend + Backend)")
        print("=" * 60)
        
        self.run_frontend_tests()
        self.run_backend_tests()
        
        self.generate_summary()
        self.save_results()
        self.print_summary()
        
        return 0 if self.results['summary']['overall_status'] == 'passed' else 1


def main():
    runner = UnitTestRunner()
    exit_code = runner.run_all_tests()
    
    print(f"\n🎉 Unit test execution completed!")
    print(f"📊 Check test-results/ directory for detailed reports")
    
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
