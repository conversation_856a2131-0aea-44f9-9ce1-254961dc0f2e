import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../test/utils'
import App from '../../App'

describe('App Component', () => {
  it('renders without crashing', () => {
    render(<App />)
    // App should render without throwing an error
    expect(document.body).toBeInTheDocument()
  })

  it('handles routing correctly', () => {
    render(<App />)
    // Basic routing test - app should handle routes
    expect(window.location.pathname).toBe('/')
  })
})
