import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockUser, mockApiResponse } from '../../test/utils'
import UserManagement from '../UserManagement'

const mockOnBack = vi.fn()

describe('UserManagement Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({
      success: true,
      data: {
        users: [mockUser],
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 1,
          has_next: false,
          has_previous: false,
        }
      }
    })
  })

  it('renders user management interface', async () => {
    render(<UserManagement onBack={mockOnBack} />)
    
    expect(screen.getByText('User Management')).toBeInTheDocument()
    expect(screen.getByText('Manage system users and their permissions')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add user/i })).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })
  })

  it('fetches users on mount', async () => {
    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin-controls/users/'),
        expect.objectContaining({
          credentials: 'include',
        })
      )
    })
  })

  it('opens create user modal when add user button is clicked', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    const addButton = screen.getByRole('button', { name: /add user/i })
    await user.click(addButton)
    
    expect(screen.getByText('Create New User')).toBeInTheDocument()
  })

  it('handles search functionality', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    const searchInput = screen.getByPlaceholderText(/search users/i)
    const searchButton = screen.getByRole('button', { name: /search/i })
    
    await user.type(searchInput, '<EMAIL>')
    await user.click(searchButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=test%40example.com'),
        expect.any(Object)
      )
    })
  })

  it('handles user selection for bulk operations', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    const checkbox = screen.getAllByRole('checkbox')[1] // First is select all
    await user.click(checkbox)
    
    expect(screen.getByText('1 user selected')).toBeInTheDocument()
  })

  it('handles select all functionality', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    const selectAllCheckbox = screen.getAllByRole('checkbox')[0]
    await user.click(selectAllCheckbox)
    
    expect(screen.getByText('1 user selected')).toBeInTheDocument()
  })

  it('performs bulk operations', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            users: [mockUser],
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 1,
              has_next: false,
              has_previous: false,
            }
          }
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Bulk action completed',
          processed: [{ id: mockUser.id, email: mockUser.email, action: 'activated' }],
          failed: []
        })
      })

    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    // Select user
    const checkbox = screen.getAllByRole('checkbox')[1]
    await user.click(checkbox)
    
    // Select bulk action
    const actionSelect = screen.getByDisplayValue('Select action...')
    await user.selectOptions(actionSelect, 'activate')
    
    // Apply action
    const applyButton = screen.getByRole('button', { name: /apply/i })
    await user.click(applyButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/users/bulk-action/'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            user_ids: [mockUser.id],
            action: 'activate'
          })
        })
      )
    })
  })

  it('handles user status toggle', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            users: [mockUser],
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 1,
              has_next: false,
              has_previous: false,
            }
          }
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, message: 'User status updated' })
      })

    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    const toggleButton = screen.getByRole('button', { name: /toggle user status/i })
    await user.click(toggleButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${mockUser.id}/toggle-status/`),
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
        })
      )
    })
  })

  it('opens edit modal when edit button is clicked', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })

    const editButton = screen.getByRole('button', { name: /edit user/i })
    await user.click(editButton)
    
    expect(screen.getByText('Edit User')).toBeInTheDocument()
  })

  it('handles pagination', async () => {
    const user = userEvent.setup()
    global.fetch = mockFetch({
      success: true,
      data: {
        users: [mockUser],
        pagination: {
          current_page: 1,
          total_pages: 2,
          total_count: 2,
          has_next: true,
          has_previous: false,
        }
      }
    })

    render(<UserManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Page 1 of 2')).toBeInTheDocument()
    })

    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).not.toBeDisabled()
    
    await user.click(nextButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('page=2'),
        expect.any(Object)
      )
    })
  })

  it('calls onBack when back button is clicked', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)
    
    const backButton = screen.getByRole('button', { name: /back/i })
    await user.click(backButton)
    
    expect(mockOnBack).toHaveBeenCalled()
  })
})
