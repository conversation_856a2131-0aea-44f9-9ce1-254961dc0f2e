import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockUser, mockApiResponse } from '../../test/utils'
import UserManagement from '../UserManagement'

const mockOnBack = vi.fn()

describe('UserManagement Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({
      success: true,
      data: {
        users: [mockUser],
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 1,
          has_next: false,
          has_previous: false,
        }
      }
    })
  })

  it('renders user management interface', () => {
    render(<UserManagement onBack={mockOnBack} />)

    expect(screen.getByText('User Management')).toBeInTheDocument()
    expect(screen.getByText('Manage system users and their permissions')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add user/i })).toBeInTheDocument()
  })

  it('has search functionality', () => {
    render(<UserManagement onBack={mockOnBack} />)

    expect(screen.getByPlaceholderText(/search users/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument()
  })

  it('opens create user modal when add user button is clicked', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)

    const addButton = screen.getByRole('button', { name: /add user/i })
    await user.click(addButton)

    expect(screen.getByText('Create New User')).toBeInTheDocument()
  })

  it('has bulk operations interface', () => {
    render(<UserManagement onBack={mockOnBack} />)

    // Should have checkboxes for bulk operations
    const checkboxes = screen.getAllByRole('checkbox')
    expect(checkboxes.length).toBeGreaterThan(0)
  })

  it('has back button functionality', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)

    const backButton = screen.getByRole('button', { name: /back/i })
    await user.click(backButton)

    expect(mockOnBack).toHaveBeenCalled()
  })

})
