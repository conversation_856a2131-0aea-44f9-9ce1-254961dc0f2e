import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockFetchError } from '../../test/utils'
import Login from '../Login'

// Mock useNavigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({ success: true, question: '2 + 2 = ?', answer: '4' })
  })

  it('renders login form correctly', () => {
    render(<Login loginType="bce" />)

    expect(screen.getByText('BCE Login')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('renders vendor login correctly', () => {
    render(<Login loginType="vendor" />)

    expect(screen.getByText('Vendor Login')).toBeInTheDocument()
  })

  it('generates captcha on mount', async () => {
    render(<Login loginType="bce" />)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/generate-captcha/',
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
        })
      )
    }, { timeout: 3000 })
  })

  it('shows password when eye icon is clicked', async () => {
    const user = userEvent.setup()
    render(<Login loginType="bce" />)

    const passwordInput = screen.getByPlaceholderText(/password/i)
    const eyeButtons = screen.getAllByRole('button')
    const eyeButton = eyeButtons.find(btn => btn.getAttribute('aria-label')?.includes('password') || btn.querySelector('svg'))

    expect(passwordInput).toHaveAttribute('type', 'password')

    if (eyeButton) {
      await user.click(eyeButton)
      expect(passwordInput).toHaveAttribute('type', 'text')
    }
  })

  it('handles form submission', async () => {
    const user = userEvent.setup()
    render(<Login loginType="bce" />)

    const emailInput = screen.getByPlaceholderText(/email/i)
    const passwordInput = screen.getByPlaceholderText(/password/i)

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')

    // Form should be ready for submission
    expect(emailInput).toHaveValue('<EMAIL>')
    expect(passwordInput).toHaveValue('password123')
  })

  it('displays captcha question when loaded', async () => {
    render(<Login loginType="bce" />)

    await waitFor(() => {
      expect(screen.getByText('2 + 2 = ?')).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('has captcha input field', () => {
    render(<Login loginType="bce" />)

    expect(screen.getByPlaceholderText(/captcha/i)).toBeInTheDocument()
  })

  it('has refresh captcha button', () => {
    render(<Login loginType="bce" />)

    const refreshButtons = screen.getAllByRole('button')
    const hasRefreshButton = refreshButtons.some(btn =>
      btn.getAttribute('title')?.includes('refresh') ||
      btn.querySelector('svg')
    )
    expect(hasRefreshButton).toBe(true)
  })
})
