import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockFetchError } from '../../test/utils'
import Login from '../Login'

// Mock useNavigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({ success: true, question: '2 + 2 = ?', answer: '4' })
  })

  it('renders login form correctly', () => {
    render(<Login loginType="bce" />)
    
    expect(screen.getByText('BCE Login')).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('renders vendor login correctly', () => {
    render(<Login loginType="vendor" />)
    
    expect(screen.getByText('Vendor Login')).toBeInTheDocument()
  })

  it('generates captcha on mount', async () => {
    render(<Login loginType="bce" />)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/generate-captcha/',
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
        })
      )
    })
  })

  it('shows password when eye icon is clicked', async () => {
    const user = userEvent.setup()
    render(<Login loginType="bce" />)
    
    const passwordInput = screen.getByLabelText(/password/i)
    const eyeButton = screen.getByRole('button', { name: /toggle password visibility/i })
    
    expect(passwordInput).toHaveAttribute('type', 'password')
    
    await user.click(eyeButton)
    expect(passwordInput).toHaveAttribute('type', 'text')
  })

  it('validates form fields before submission', async () => {
    const user = userEvent.setup()
    render(<Login loginType="bce" />)
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)
    
    expect(screen.getByText(/email is required/i)).toBeInTheDocument()
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, question: '2 + 2 = ?', answer: '4' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, message: 'Login successful' })
      })

    render(<Login loginType="bce" />)
    
    await waitFor(() => {
      expect(screen.getByText('2 + 2 = ?')).toBeInTheDocument()
    })

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.type(screen.getByLabelText(/captcha/i), '4')
    
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/auth/login/',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
            captcha_answer: '4',
            login_type: 'bce'
          })
        })
      )
    })
  })

  it('handles login error', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, question: '2 + 2 = ?', answer: '4' })
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ success: false, error: 'Invalid credentials' })
      })

    render(<Login loginType="bce" />)
    
    await waitFor(() => {
      expect(screen.getByText('2 + 2 = ?')).toBeInTheDocument()
    })

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'wrongpassword')
    await user.type(screen.getByLabelText(/captcha/i), '4')
    
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
    })
  })

  it('refreshes captcha when refresh button is clicked', async () => {
    const user = userEvent.setup()
    global.fetch = mockFetch({ success: true, question: '3 + 3 = ?', answer: '6' })
    
    render(<Login loginType="bce" />)
    
    await waitFor(() => {
      expect(screen.getByText('2 + 2 = ?')).toBeInTheDocument()
    })

    const refreshButton = screen.getByRole('button', { name: /refresh captcha/i })
    await user.click(refreshButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2)
    })
  })
})
