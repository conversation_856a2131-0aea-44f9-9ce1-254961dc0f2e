import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockGroup } from '../../test/utils'
import GroupManagement from '../GroupManagement'

const mockOnBack = vi.fn()

describe('GroupManagement Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({
      success: true,
      data: {
        groups: [mockGroup],
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 1,
          has_next: false,
          has_previous: false,
        }
      }
    })
  })

  it('renders group management interface', async () => {
    render(<GroupManagement onBack={mockOnBack} />)
    
    expect(screen.getByText('Group Management')).toBeInTheDocument()
    expect(screen.getByText('Manage user groups and permissions')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add group/i })).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Test Group')).toBeInTheDocument()
    })
  })

  it('fetches groups on mount', async () => {
    render(<GroupManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin-controls/groups/'),
        expect.objectContaining({
          credentials: 'include',
        })
      )
    })
  })

  it('opens create group modal when add group button is clicked', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)
    
    const addButton = screen.getByRole('button', { name: /add group/i })
    await user.click(addButton)
    
    expect(screen.getByText('Add New Group')).toBeInTheDocument()
  })

  it('creates a new group', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            groups: [mockGroup],
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 1,
              has_next: false,
              has_previous: false,
            }
          }
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Group created successfully'
        })
      })

    render(<GroupManagement onBack={mockOnBack} />)
    
    const addButton = screen.getByRole('button', { name: /add group/i })
    await user.click(addButton)
    
    const nameInput = screen.getByLabelText(/group name/i)
    await user.type(nameInput, 'New Test Group')
    
    const createButton = screen.getByRole('button', { name: /create group/i })
    await user.click(createButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/groups/create/'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ name: 'New Test Group' })
        })
      )
    })
  })

  it('handles search functionality', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)
    
    const searchInput = screen.getByPlaceholderText(/search groups/i)
    const searchButton = screen.getByRole('button', { name: /search/i })
    
    await user.type(searchInput, 'test')
    await user.click(searchButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=test'),
        expect.any(Object)
      )
    })
  })

  it('opens group details when group name is clicked', async () => {
    const user = userEvent.setup()
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            groups: [mockGroup],
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 1,
              has_next: false,
              has_previous: false,
            }
          }
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          group: {
            ...mockGroup,
            members: [
              {
                id: '123',
                email: '<EMAIL>',
                full_name: 'Test User',
                user_type: 'user',
                is_active: true,
              }
            ],
            permissions: []
          }
        })
      })

    render(<GroupManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Test Group')).toBeInTheDocument()
    })

    const groupButton = screen.getByRole('button', { name: 'Test Group' })
    await user.click(groupButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining(`/groups/${mockGroup.id}/`),
        expect.objectContaining({
          credentials: 'include',
        })
      )
    })
  })

  it('handles pagination', async () => {
    const user = userEvent.setup()
    global.fetch = mockFetch({
      success: true,
      data: {
        groups: [mockGroup],
        pagination: {
          current_page: 1,
          total_pages: 2,
          total_count: 2,
          has_next: true,
          has_previous: false,
        }
      }
    })

    render(<GroupManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('Page 1 of 2')).toBeInTheDocument()
    })

    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).not.toBeDisabled()
    
    await user.click(nextButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('page=2'),
        expect.any(Object)
      )
    })
  })

  it('validates group name before creation', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)
    
    const addButton = screen.getByRole('button', { name: /add group/i })
    await user.click(addButton)
    
    const createButton = screen.getByRole('button', { name: /create group/i })
    await user.click(createButton)
    
    expect(screen.getByText(/group name is required/i)).toBeInTheDocument()
  })

  it('calls onBack when back button is clicked', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)
    
    const backButton = screen.getByRole('button', { name: /back/i })
    await user.click(backButton)
    
    expect(mockOnBack).toHaveBeenCalled()
  })

  it('displays group statistics correctly', async () => {
    render(<GroupManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText('5 permissions')).toBeInTheDocument()
      expect(screen.getByText('3 users')).toBeInTheDocument()
    })
  })

  it('handles error states', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 403,
      json: () => Promise.resolve({ success: false, error: 'Permission denied' })
    })

    render(<GroupManagement onBack={mockOnBack} />)
    
    await waitFor(() => {
      expect(screen.getByText(/permission denied/i)).toBeInTheDocument()
    })
  })
})
