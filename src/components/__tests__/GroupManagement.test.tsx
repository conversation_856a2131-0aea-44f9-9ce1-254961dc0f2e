import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockGroup } from '../../test/utils'
import GroupManagement from '../GroupManagement'

const mockOnBack = vi.fn()

describe('GroupManagement Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({
      success: true,
      data: {
        groups: [mockGroup],
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 1,
          has_next: false,
          has_previous: false,
        }
      }
    })
  })

  it('renders group management interface', () => {
    render(<GroupManagement onBack={mockOnBack} />)

    expect(screen.getByText('Group Management')).toBeInTheDocument()
    expect(screen.getByText('Manage user groups and permissions')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add group/i })).toBeInTheDocument()
  })

  it('has search functionality', () => {
    render(<GroupManagement onBack={mockOnBack} />)

    expect(screen.getByPlaceholderText(/search groups/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument()
  })

  it('opens create group modal when add group button is clicked', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)

    const addButton = screen.getByRole('button', { name: /add group/i })
    await user.click(addButton)

    expect(screen.getByText('Add New Group')).toBeInTheDocument()
  })

  it('has back button functionality', async () => {
    const user = userEvent.setup()
    render(<GroupManagement onBack={mockOnBack} />)

    const backButton = screen.getByRole('button', { name: /back/i })
    await user.click(backButton)

    expect(mockOnBack).toHaveBeenCalled()
  })
})
