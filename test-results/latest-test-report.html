
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talent Hero Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .summary {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .status-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .status-card p {
            margin: 0;
            font-size: 1.5em;
            text-transform: uppercase;
        }
        .details {
            padding: 0 30px 30px 30px;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            font-size: 1.1em;
        }
        .section-content {
            padding: 20px;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Talent Hero Test Report</h1>
            <p>Comprehensive Testing Results</p>
        </div>
        
        <div class="summary">
            <div class="status-card" style="background-color: #dc3545">
                <h3>Overall Status</h3>
                <p>FAILED</p>
            </div>
            <div class="status-card" style="background-color: #ffc107">
                <h3>Frontend Tests</h3>
                <p>ERROR</p>
            </div>
            <div class="status-card" style="background-color: #dc3545">
                <h3>Backend Tests</h3>
                <p>FAILED</p>
            </div>
        </div>
        
        <div class="details">
            <div class="section">
                <div class="section-header">🎨 Frontend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> error</p>
                    <p><strong>Return Code:</strong> N/A</p>
                    
                    
                    
                    
                    
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">⚙️ Backend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> failed</p>
                    <p><strong>Return Code:</strong> 1</p>
                    
                    
            <h4>Backend Output</h4>
            <div class="test-output">Found 54 test(s).
Operations to perform:
  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles
  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions
Synchronizing apps without migrations:
  Creating tables...
    Running deferred SQL...
Running migrations:
  No migrations to apply.
System check identified no issues (0 silenced).


test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration) failed:

    NoReverseMatch("Reverse for 'generate_captcha' not found.
    'generate_captcha' is not a valid view function or pattern name.")

Unfortunately, tracebacks cannot be pickled, making it impossible for the
parallel test runner to handle this exception cleanly.

In order to see the traceback, you should install tblib:

    python -m pip install tblib



test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate) failed:

    NoReverseMatch("Reverse for 'bulk_user_action' not found.
    'bulk_user_action' is not a valid view function or pattern name.")

Unfortunately, tracebacks cannot be pickled, making it impossible for the
parallel test runner to handle this exception cleanly.

In order to see the traceback, you should install tblib:

    python -m pip install tblib



test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions) failed:

    NoReverseMatch("Reverse for 'list_users' not found. 'list_users' is not
    a valid view function or pattern name.")

Unfortunately, tracebacks cannot be pickled, making it impossible for the
parallel test runner to handle this exception cleanly.

In order to see the traceback, you should install tblib:

    python -m pip install tblib



test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group) failed:

    NoReverseMatch("Reverse for 'add_user_to_group' not found.
    'add_user_to_group' is not a valid view function or pattern name.")

Unfortunately, tracebacks cannot be pickled, making it impossible for the
parallel test runner to handle this exception cleanly.

In order to see the traceback, you should install tblib:

    python -m pip install tblib



test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login) failed:

    NoReverseMatch("Reverse for 'generate_captcha' not found.
    'generate_captcha' is not a valid view function or pattern name.")

Unfortunately, tracebacks cannot be pickled, making it impossible for the
parallel test runner to handle this exception cleanly.

In order to see the traceback, you should install tblib:

    python -m pip install tblib

</div>
        
                    
            <h4>Backend Errors</h4>
            <div class="error">Using existing test database for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Using existing clone for alias 'default' ('test_th')...

Preserving test database for alias 'default' ('test_th_1')...

Preserving test database for alias 'default' ('test_th_2')...

Preserving test database for alias 'default' ('test_th_3')...

Preserving test database for alias 'default' ('test_th_4')...

Preserving test database for alias 'default' ('test_th_5')...

Preserving test database for alias 'default' ('test_th_6')...

Preserving test database for alias 'default' ('test_th_7')...

Preserving test database for alias 'default' ('test_th_8')...

Preserving test database for alias 'default' ('test_th_9')...

Preserving test database for alias 'default' ('test_th_10')...

Preserving test database for alias 'default' ('test_th')...

multiprocessing.pool.RemoteTraceback: 
"""
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 58, in testPartExecutor
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 651, in run
    self._callTestMethod(testMethod)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 606, in _callTestMethod
    if method() is not None:
       ~~~~~~^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 35, in test_captcha_regeneration
    response1 = self.client.post(reverse('generate_captcha'))
                                 ~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\pool.py", line 125, in worker
    result = (True, func(*args, **kwds))
                    ~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 466, in _run_subsuite
    result = runner.run(subsuite)
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 381, in run
    test(result)
    ~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\suite.py", line 84, in __call__
    return self.run(*args, **kwds)
           ~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\suite.py", line 122, in run
    test(result)
    ~~~~^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\testcases.py", line 321, in __call__
    self._setup_and_call(result)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\testcases.py", line 376, in _setup_and_call
    super().__call__(result)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 707, in __call__
    return self.run(*args, **kwds)
           ~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 650, in run
    with outcome.testPartExecutor(self):
         ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 75, in testPartExecutor
    _addError(self.result, test_case, exc_info)
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\case.py", line 100, in _addError
    result.addError(test, exc_info)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 298, in addError
    self.check_picklable(test, err)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 219, in check_picklable
    self._confirm_picklable(err)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 189, in _confirm_picklable
    pickle.loads(pickle.dumps(obj))
                 ~~~~~~~~~~~~^^^^^
TypeError: cannot pickle 'traceback' object
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\commands\test.py", line 24, in run_from_argv
    super().run_from_argv(argv)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\base.py", line 416, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\base.py", line 460, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\core\management\commands\test.py", line 63, in handle
    failures = test_runner.run_tests(test_labels)
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 1099, in run_tests
    result = self.run_suite(suite)
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 1026, in run_suite
    return runner.run(suite)
           ~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\runner.py", line 240, in run
    test(result)
    ~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\unittest\suite.py", line 84, in __call__
    return self.run(*args, **kwds)
           ~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\untitled\.venv\Lib\site-packages\django\test\runner.py", line 553, in run
    subsuite_index, events = test_results.next(timeout=0.1)
                             ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\pool.py", line 873, in next
    raise value
TypeError: cannot pickle 'traceback' object
</div>
        
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Generated on: 2025-07-15 00:23:36
        </div>
    </div>
</body>
</html>
        