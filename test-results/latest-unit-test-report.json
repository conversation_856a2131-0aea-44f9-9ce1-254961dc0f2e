{"timestamp": "2025-07-15T09:09:26.133931", "frontend": {"status": "failed", "return_code": 1, "stdout": "\n> th-v3-11@0.0.0 test:run\n> vitest run\n\n\n\u001b[1m\u001b[46m RUN \u001b[49m\u001b[22m \u001b[36mv3.2.4 \u001b[39m\u001b[90mC:/Users/<USER>/IdeaProjects/th-v3-11\u001b[39m\n\n \u001b[32m✓\u001b[39m src/components/__tests__/Simple.test.tsx \u001b[2m(\u001b[22m\u001b[2m5 tests\u001b[22m\u001b[2m)\u001b[22m\u001b[32m 3\u001b[2mms\u001b[22m\u001b[39m\n \u001b[31m❯\u001b[39m src/components/__tests__/App.test.tsx \u001b[2m(\u001b[22m\u001b[2m2 tests\u001b[22m\u001b[2m | \u001b[22m\u001b[31m2 failed\u001b[39m\u001b[2m)\u001b[22m\u001b[32m 17\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m App Component\u001b[2m > \u001b[22mrenders without crashing\u001b[39m\u001b[32m 14\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → You cannot render a <Router> inside another <Router>. You should never have more than one in your app.\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m App Component\u001b[2m > \u001b[22mhandles routing correctly\u001b[39m\u001b[32m 2\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → You cannot render a <Router> inside another <Router>. You should never have more than one in your app.\u001b[39m\n \u001b[31m❯\u001b[39m src/components/__tests__/GroupManagement.test.tsx \u001b[2m(\u001b[22m\u001b[2m5 tests\u001b[22m\u001b[2m | \u001b[22m\u001b[31m5 failed\u001b[39m\u001b[2m)\u001b[22m\u001b[32m 61\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m GroupManagement Component\u001b[2m > \u001b[22mrenders group management interface\u001b[39m\u001b[32m 23\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the text: Group Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m GroupManagement Component\u001b[2m > \u001b[22mhas search functionality\u001b[39m\u001b[32m 4\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the placeholder text of: /search groups/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m GroupManagement Component\u001b[2m > \u001b[22mhas table structure\u001b[39m\u001b[32m 3\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the text: Group Name. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m GroupManagement Component\u001b[2m > \u001b[22mopens create group modal when add group button is clicked\u001b[39m\u001b[32m 26\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an accessible element with the role \"button\" and name `/add group/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m GroupManagement Component\u001b[2m > \u001b[22mhas back button functionality\u001b[39m\u001b[32m 5\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n \u001b[31m❯\u001b[39m src/components/__tests__/UserManagement.test.tsx \u001b[2m(\u001b[22m\u001b[2m6 tests\u001b[22m\u001b[2m | \u001b[22m\u001b[31m6 failed\u001b[39m\u001b[2m)\u001b[22m\u001b[32m 72\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mrenders user management interface\u001b[39m\u001b[32m 22\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the text: User Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mhas search functionality\u001b[39m\u001b[32m 6\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the placeholder text of: /search users/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mhas search button\u001b[39m\u001b[32m 29\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an accessible element with the role \"button\"\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mopens create user modal when add user button is clicked\u001b[39m\u001b[32m 7\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an accessible element with the role \"button\" and name `/add user/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mhas table structure\u001b[39m\u001b[32m 2\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an element with the text: Email. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[31m   \u001b[31m×\u001b[31m UserManagement Component\u001b[2m > \u001b[22mhas back button functionality\u001b[39m\u001b[32m 5\u001b[2mms\u001b[22m\u001b[39m\n\u001b[31m     → Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\n\u001b[2m Test Files \u001b[22m \u001b[1m\u001b[31m7 failed\u001b[39m\u001b[22m\u001b[2m | \u001b[22m\u001b[1m\u001b[32m1 passed\u001b[39m\u001b[22m\u001b[90m (8)\u001b[39m\n\u001b[2m      Tests \u001b[22m \u001b[1m\u001b[31m13 failed\u001b[39m\u001b[22m\u001b[2m | \u001b[22m\u001b[1m\u001b[32m5 passed\u001b[39m\u001b[22m\u001b[90m (18)\u001b[39m\n\u001b[2m   Start at \u001b[22m 09:09:26\n\u001b[2m   Duration \u001b[22m 2.19s\u001b[2m (transform 228ms, setup 753ms, collect 741ms, tests 153ms, environment 3.51s, prepare 953ms)\u001b[22m\n\n", "stderr": "\u001b[90mstderr\u001b[2m | src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mGroupManagement Component\u001b[2m > \u001b[22m\u001b[2mrenders group management interface\n\u001b[22m\u001b[39mAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mGroupManagement Component\u001b[2m > \u001b[22m\u001b[2mhas search functionality\n\u001b[22m\u001b[39mAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mGroupManagement Component\u001b[2m > \u001b[22m\u001b[2mhas table structure\n\u001b[22m\u001b[39mAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mrenders user management interface\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mhas search functionality\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mGroupManagement Component\u001b[2m > \u001b[22m\u001b[2mopens create group modal when add group button is clicked\n\u001b[22m\u001b[39mAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mGroupManagement Component\u001b[2m > \u001b[22m\u001b[2mhas back button functionality\n\u001b[22m\u001b[39mAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to GroupManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mhas search button\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mopens create user modal when add user button is clicked\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mhas table structure\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\u001b[90mstderr\u001b[2m | src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22m\u001b[2mUserManagement Component\u001b[2m > \u001b[22m\u001b[2mhas back button functionality\n\u001b[22m\u001b[39mAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\nAn update to UserManagement inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act\n\n\n\u001b[31m⎯⎯⎯⎯⎯⎯\u001b[39m\u001b[1m\u001b[41m Failed Suites 4 \u001b[49m\u001b[22m\u001b[31m⎯⎯⎯⎯⎯⎯⎯\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m tests/e2e/admin.spec.ts\u001b[2m [ tests/e2e/admin.spec.ts ]\u001b[22m\n\u001b[31m\u001b[1mError\u001b[22m: Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._currentSuite node_modules/playwright/lib/common/testType.js:\u001b[2m74:13\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._describe node_modules/playwright/lib/common/testType.js:\u001b[2m114:24\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Function.describe node_modules/playwright/lib/transform/transform.js:\u001b[2m275:12\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m tests/e2e/admin.spec.ts:\u001b[2m26:6\u001b[22m\u001b[39m\n    \u001b[90m 24| \u001b[39m}\n    \u001b[90m 25| \u001b[39m\n    \u001b[90m 26| \u001b[39mtest\u001b[33m.\u001b[39m\u001b[34mdescribe\u001b[39m(\u001b[32m'Admin Dashboard'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n    \u001b[90m   | \u001b[39m     \u001b[31m^\u001b[39m\n    \u001b[90m 27| \u001b[39m  test\u001b[33m.\u001b[39m\u001b[34mbeforeEach\u001b[39m(\u001b[35masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n    \u001b[90m 28| \u001b[39m    \u001b[90m// Mock successful login response\u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m tests/e2e/auth.spec.ts\u001b[2m [ tests/e2e/auth.spec.ts ]\u001b[22m\n\u001b[31m\u001b[1mError\u001b[22m: Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._currentSuite node_modules/playwright/lib/common/testType.js:\u001b[2m74:13\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._describe node_modules/playwright/lib/common/testType.js:\u001b[2m114:24\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Function.describe node_modules/playwright/lib/transform/transform.js:\u001b[2m275:12\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m tests/e2e/auth.spec.ts:\u001b[2m3:6\u001b[22m\u001b[39m\n    \u001b[90m  1| \u001b[39m\u001b[35mimport\u001b[39m { test\u001b[33m,\u001b[39m expect } \u001b[35mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[90m  2| \u001b[39m\n    \u001b[90m  3| \u001b[39mtest\u001b[33m.\u001b[39m\u001b[34mdescribe\u001b[39m(\u001b[32m'Authentication Flow'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n    \u001b[90m   | \u001b[39m     \u001b[31m^\u001b[39m\n    \u001b[90m  4| \u001b[39m  test\u001b[33m.\u001b[39m\u001b[34mbeforeEach\u001b[39m(\u001b[35masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n    \u001b[90m  5| \u001b[39m    \u001b[90m// Navigate to the BCE login page\u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m tests/e2e/user-workflow.spec.ts\u001b[2m [ tests/e2e/user-workflow.spec.ts ]\u001b[22m\n\u001b[31m\u001b[1mError\u001b[22m: Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._currentSuite node_modules/playwright/lib/common/testType.js:\u001b[2m74:13\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TestTypeImpl._describe node_modules/playwright/lib/common/testType.js:\u001b[2m114:24\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Function.describe node_modules/playwright/lib/transform/transform.js:\u001b[2m275:12\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m tests/e2e/user-workflow.spec.ts:\u001b[2m4:6\u001b[22m\u001b[39m\n    \u001b[90m  2| \u001b[39m\u001b[35mimport\u001b[39m { \u001b[33mTestHelpers\u001b[39m } \u001b[35mfrom\u001b[39m \u001b[32m'./utils/test-helpers'\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[90m  3| \u001b[39m\n    \u001b[90m  4| \u001b[39mtest\u001b[33m.\u001b[39m\u001b[34mdescribe\u001b[39m(\u001b[32m'Complete User Workflow'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n    \u001b[90m   | \u001b[39m     \u001b[31m^\u001b[39m\n    \u001b[90m  5| \u001b[39m  \u001b[35mlet\u001b[39m helpers\u001b[33m:\u001b[39m \u001b[33mTestHelpers\u001b[39m\u001b[33m;\u001b[39m\n    \u001b[90m  6| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[3/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/Login.test.tsx\u001b[2m [ src/components/__tests__/Login.test.tsx ]\u001b[22m\n\u001b[31m\u001b[1mError\u001b[22m: Failed to resolve import \"../Login\" from \"src/components/__tests__/Login.test.tsx\". Does the file exist?\u001b[39m\n  Plugin: \u001b[35mvite:import-analysis\u001b[39m\n  File: \u001b[36mC:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/Login.test.tsx\u001b[39m:5:0\n\u001b[33m  10 |  const __vi_import_2__ = await import(\"@testing-library/user-event\");\n  11 |  const __vi_import_3__ = await import(\"../../test/utils\");\n  12 |  const __vi_import_4__ = await import(\"../Login\");\n     |                                       ^\n  13 |  \n  14 |  import { describe, it, expect, vi, beforeEach } from \"vitest\";\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TransformPluginContext._formatLog ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m31435:43\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TransformPluginContext.error ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m31432:14\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m normalizeUrl ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m29978:18\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m30036:32\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m TransformPluginContext.transform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m30004:4\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m EnvironmentPluginContainer.transform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m31249:14\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m loadAndTransform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:\u001b[2m26419:26\u001b[22m\u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[4/17]⎯\u001b[22m\u001b[39m\n\n\n\u001b[31m⎯⎯⎯⎯⎯⎯\u001b[39m\u001b[1m\u001b[41m Failed Tests 13 \u001b[49m\u001b[22m\u001b[31m⎯⎯⎯⎯⎯⎯⎯\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/App.test.tsx\u001b[2m > \u001b[22mApp Component\u001b[2m > \u001b[22mrenders without crashing\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/App.test.tsx\u001b[2m > \u001b[22mApp Component\u001b[2m > \u001b[22mhandles routing correctly\n\u001b[31m\u001b[1mError\u001b[22m: You cannot render a <Router> inside another <Router>. You should never have more than one in your app.\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m invariant ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:\u001b[2m188:11\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Router ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:\u001b[2m5746:3\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m23863:20\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m5529:22\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m8897:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m beginWork node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m10522:18\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m1522:13\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m15140:22\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m14956:41\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m renderRootSync node_modules/react-dom/cjs/react-dom-client.development.js:\u001b[2m14936:11\u001b[22m\u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[5/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22mGroupManagement Component\u001b[2m > \u001b[22mrenders group management interface\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the text: Group Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/GroupManagement.test.tsx:\u001b[2m30:19\u001b[22m\u001b[39m\n    \u001b[90m 28| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mGroupManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 29| \u001b[39m\n    \u001b[90m 30| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Group Management'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 31| \u001b[39m    expect(screen.getByText('Manage user groups and permissions')).toB…\n    \u001b[90m 32| \u001b[39m    expect(screen.getByRole('button', { name: /add group/i })).toBeInT…\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[6/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22mGroupManagement Component\u001b[2m > \u001b[22mhas search functionality\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the placeholder text of: /search groups/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/GroupManagement.test.tsx:\u001b[2m38:19\u001b[22m\u001b[39m\n    \u001b[90m 36| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mGroupManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 37| \u001b[39m\n    \u001b[90m 38| \u001b[39m    expect(screen.getByPlaceholderText(/search groups/i)).toBeInTheDoc…\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 39| \u001b[39m  })\n    \u001b[90m 40| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[7/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22mGroupManagement Component\u001b[2m > \u001b[22mhas table structure\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the text: Group Name. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/GroupManagement.test.tsx:\u001b[2m45:19\u001b[22m\u001b[39m\n    \u001b[90m 43| \u001b[39m\n    \u001b[90m 44| \u001b[39m    \u001b[90m// Should have table headers\u001b[39m\n    \u001b[90m 45| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Group Name'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 46| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Permissions'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m 47| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Members'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[8/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22mGroupManagement Component\u001b[2m > \u001b[22mopens create group modal when add group button is clicked\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an accessible element with the role \"button\" and name `/add group/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/GroupManagement.test.tsx:\u001b[2m55:30\u001b[22m\u001b[39m\n    \u001b[90m 53| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mGroupManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 54| \u001b[39m\n    \u001b[90m 55| \u001b[39m    const addButton = screen.getByRole('button', { name: /add group/i …\n    \u001b[90m   | \u001b[39m                             \u001b[31m^\u001b[39m\n    \u001b[90m 56| \u001b[39m    \u001b[35mawait\u001b[39m user\u001b[33m.\u001b[39m\u001b[34mclick\u001b[39m(addButton)\n    \u001b[90m 57| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[9/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/GroupManagement.test.tsx\u001b[2m > \u001b[22mGroupManagement Component\u001b[2m > \u001b[22mhas back button functionality\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"text-muted-foreground\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/GroupManagement.test.tsx:\u001b[2m65:31\u001b[22m\u001b[39m\n    \u001b[90m 63| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mGroupManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 64| \u001b[39m\n    \u001b[90m 65| \u001b[39m    \u001b[35mconst\u001b[39m backButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39m\u001b[34mgetByRole\u001b[39m(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[36m/back/i\u001b[39m })\n    \u001b[90m   | \u001b[39m                              \u001b[31m^\u001b[39m\n    \u001b[90m 66| \u001b[39m    \u001b[35mawait\u001b[39m user\u001b[33m.\u001b[39m\u001b[34mclick\u001b[39m(backButton)\n    \u001b[90m 67| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[10/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mrenders user management interface\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the text: User Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m30:19\u001b[22m\u001b[39m\n    \u001b[90m 28| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mUserManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 29| \u001b[39m\n    \u001b[90m 30| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'User Management'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 31| \u001b[39m    expect(screen.getByText('Manage system users and their permissions…\n    \u001b[90m 32| \u001b[39m    expect(screen.getByRole('button', { name: /add user/i })).toBeInTh…\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[11/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mhas search functionality\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the placeholder text of: /search users/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m38:19\u001b[22m\u001b[39m\n    \u001b[90m 36| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mUserManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 37| \u001b[39m\n    \u001b[90m 38| \u001b[39m    expect(screen.getByPlaceholderText(/search users/i)).toBeInTheDocu…\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 39| \u001b[39m  })\n    \u001b[90m 40| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[12/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mhas search button\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an accessible element with the role \"button\"\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m109:15\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m44:28\u001b[22m\u001b[39m\n    \u001b[90m 42| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mUserManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 43| \u001b[39m\n    \u001b[90m 44| \u001b[39m    \u001b[35mconst\u001b[39m buttons \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39m\u001b[34mgetAllByRole\u001b[39m(\u001b[32m'button'\u001b[39m)\n    \u001b[90m   | \u001b[39m                           \u001b[31m^\u001b[39m\n    \u001b[90m 45| \u001b[39m    \u001b[35mconst\u001b[39m hasSearchButton \u001b[33m=\u001b[39m buttons\u001b[33m.\u001b[39m\u001b[34msome\u001b[39m(btn \u001b[33m=>\u001b[39m\n    \u001b[90m 46| \u001b[39m      btn\u001b[33m.\u001b[39mtextContent\u001b[33m?.\u001b[39m\u001b[34mtoLowerCase\u001b[39m()\u001b[33m.\u001b[39m\u001b[34mincludes\u001b[39m(\u001b[32m'search'\u001b[39m) \u001b[33m||\u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[13/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mopens create user modal when add user button is clicked\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an accessible element with the role \"button\" and name `/add user/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m56:30\u001b[22m\u001b[39m\n    \u001b[90m 54| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mUserManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 55| \u001b[39m\n    \u001b[90m 56| \u001b[39m    \u001b[35mconst\u001b[39m addButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39m\u001b[34mgetByRole\u001b[39m(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[36m/add user/i\u001b[39m })\n    \u001b[90m   | \u001b[39m                             \u001b[31m^\u001b[39m\n    \u001b[90m 57| \u001b[39m    \u001b[35mawait\u001b[39m user\u001b[33m.\u001b[39m\u001b[34mclick\u001b[39m(addButton)\n    \u001b[90m 58| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[14/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mhas table structure\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an element with the text: Email. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m66:19\u001b[22m\u001b[39m\n    \u001b[90m 64| \u001b[39m\n    \u001b[90m 65| \u001b[39m    \u001b[90m// Should have table headers\u001b[39m\n    \u001b[90m 66| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Email'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m   | \u001b[39m                  \u001b[31m^\u001b[39m\n    \u001b[90m 67| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Name'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n    \u001b[90m 68| \u001b[39m    \u001b[34mexpect\u001b[39m(screen\u001b[33m.\u001b[39m\u001b[34mgetByText\u001b[39m(\u001b[32m'Type'\u001b[39m))\u001b[33m.\u001b[39m\u001b[34mtoBeInTheDocument\u001b[39m()\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[15/17]⎯\u001b[22m\u001b[39m\n\n\u001b[41m\u001b[1m FAIL \u001b[22m\u001b[49m src/components/__tests__/UserManagement.test.tsx\u001b[2m > \u001b[22mUserManagement Component\u001b[2m > \u001b[22mhas back button functionality\n\u001b[31m\u001b[1mTestingLibraryElementError\u001b[22m: Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[31m\n  \u001b[36m<div>\u001b[31m\n    \u001b[36m<div\u001b[31m\n      \u001b[33mclass\u001b[31m=\u001b[32m\"p-6\"\u001b[31m\n    \u001b[36m>\u001b[31m\n      \u001b[36m<div\u001b[31m\n        \u001b[33mclass\u001b[31m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[31m\n      \u001b[36m>\u001b[31m\n        \u001b[36m<div\u001b[31m\n          \u001b[33mclass\u001b[31m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[31m\n        \u001b[36m/>\u001b[31m\n      \u001b[36m</div>\u001b[31m\n    \u001b[36m</div>\u001b[31m\n  \u001b[36m</div>\u001b[31m\n\u001b[36m</body>\u001b[31m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:\u001b[2m37:19\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m76:38\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m52:17\u001b[22m\u001b[39m\n\u001b[90m \u001b[2m❯\u001b[22m node_modules/@testing-library/dom/dist/query-helpers.js:\u001b[2m95:19\u001b[22m\u001b[39m\n\u001b[36m \u001b[2m❯\u001b[22m src/components/__tests__/UserManagement.test.tsx:\u001b[2m76:31\u001b[22m\u001b[39m\n    \u001b[90m 74| \u001b[39m    \u001b[34mrender\u001b[39m(\u001b[33m<\u001b[39m\u001b[33mUserManagement\u001b[39m \u001b[33monBack\u001b[39m\u001b[33m=\u001b[39m\u001b[33m{\u001b[39mmockOnBack\u001b[33m}\u001b[39m \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m)\n    \u001b[90m 75| \u001b[39m\n    \u001b[90m 76| \u001b[39m    \u001b[35mconst\u001b[39m backButton \u001b[33m=\u001b[39m screen\u001b[33m.\u001b[39m\u001b[34mgetByRole\u001b[39m(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[36m/back/i\u001b[39m })\n    \u001b[90m   | \u001b[39m                              \u001b[31m^\u001b[39m\n    \u001b[90m 77| \u001b[39m    \u001b[35mawait\u001b[39m user\u001b[33m.\u001b[39m\u001b[34mclick\u001b[39m(backButton)\n    \u001b[90m 78| \u001b[39m\n\n\u001b[31m\u001b[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[16/17]⎯\u001b[22m\u001b[39m\n\n"}, "backend": {"status": "failed", "return_code": 1, "stdout": "Found 61 test(s).\nOperations to perform:\n  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles\n  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions\nSynchronizing apps without migrations:\n  Creating tables...\n    Running deferred SQL...\nRunning migrations:\n  No migrations to apply.\nSystem check identified no issues (0 silenced).\n", "stderr": "Using existing test database for alias 'default' ('test_th')...\n\ntest_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)\nTest adding user to group ... ERROR\ntest_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)\nTest creating group ... ERROR\ntest_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)\nTest creating group with duplicate name ... ERROR\ntest_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)\nTest getting group details ... ERROR\ntest_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)\nTest group operations without admin permission ... ERROR\ntest_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)\nTest listing groups ... ERROR\ntest_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)\nTest removing user from group ... ERROR\ntest_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)\nTest admin permissions ... ERROR\ntest_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)\nTest user hierarchy permissions ... ERROR\ntest_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)\nTest regular user restrictions ... ERROR\ntest_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)\nTest superuser has all permissions ... ERROR\ntest_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)\nTest bulk activate users ... ERROR\ntest_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)\nTest bulk delete users ... ERROR\ntest_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)\nTest creating user as admin ... ERROR\ntest_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)\nTest creating user with duplicate email ... ERROR\ntest_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)\nTest creating user with invalid data ... ERROR\ntest_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)\nTest creating user and assigning to group ... ERROR\ntest_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)\nTest getting user details ... ERROR\ntest_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)\nTest listing users as admin ... ERROR\ntest_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)\nTest listing users without admin permission ... ERROR\ntest_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)\nTest toggling user status ... ERROR\ntest_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)\nTest updating user ... ERROR\ntest_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)\nTest that new captcha replaces old one ... ERROR\ntest_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)\nTest that captcha answer is stored in session ... ERROR\ntest_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)\nTest captcha generation ... ERROR\ntest_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)\nTest login with inactive user ... ERROR\ntest_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)\nTest login with invalid captcha ... ERROR\ntest_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)\nTest login with invalid credentials ... ERROR\ntest_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)\nTest login with missing required fields ... ERROR\ntest_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)\nTest successful BCE login ... ERROR\ntest_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)\nTest successful vendor login ... ERROR\ntest_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)\nTest that user permissions are included in login response ... ERROR\ntest_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)\nTest BCE user trying to login via vendor ... ERROR\ntest_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)\nTest logout without being logged in ... ERROR\ntest_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)\nTest successful logout ... ERROR\ntest_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)\nTest that session is cleaned up on logout ... ERROR\ntest_session_creation_on_login (tests.test_auth.SessionTestCase.test_session_creation_on_login)\nTest that session is created on login ... ok\ntest_full_name_property (tests.test_auth.UserModelTestCase.test_full_name_property)\nTest full_name property ... ok\ntest_user_creation (tests.test_auth.UserModelTestCase.test_user_creation)\nTest user creation with custom fields ... ok\ntest_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)\nTest user permission methods ... FAIL\ntest_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)\nTest user string representation ... FAIL\ntest_group_creation (tests.test_models.GroupModelTestCase.test_group_creation)\nTest creating groups ... ok\ntest_group_name_uniqueness (tests.test_models.GroupModelTestCase.test_group_name_uniqueness)\nTest that group names must be unique ... ok\ntest_group_permissions (tests.test_models.GroupModelTestCase.test_group_permissions)\nTest group permissions ... ok\ntest_group_user_relationship (tests.test_models.GroupModelTestCase.test_group_user_relationship)\nTest group-user many-to-many relationship ... ok\ntest_email_uniqueness (tests.test_models.UserModelTestCase.test_email_uniqueness)\nTest that email addresses must be unique ... ok\ntest_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)\nTest full_name property ... FAIL\ntest_user_active_status (tests.test_models.UserModelTestCase.test_user_active_status)\nTest user active status ... ok\ntest_user_creation_with_all_fields (tests.test_models.UserModelTestCase.test_user_creation_with_all_fields)\nTest creating user with all fields ... ok\ntest_user_creation_with_required_fields (tests.test_models.UserModelTestCase.test_user_creation_with_required_fields)\nTest creating user with required fields only ... ok\ntest_user_groups (tests.test_models.UserModelTestCase.test_user_groups)\nTest user group relationships ... ok\ntest_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)\nTest user permission hierarchy ... FAIL\ntest_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)\nTest user __str__ method ... FAIL\ntest_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)\nTest user type validation ... ERROR\ntest_user_creation (tests.test_simple.DatabaseTestCase.test_user_creation)\nTest that we can create users ... ok\ntest_user_query (tests.test_simple.DatabaseTestCase.test_user_query)\nTest that we can query users ... ok\ntest_user_relationships (tests.test_simple.DatabaseTestCase.test_user_relationships)\nTest user relationships work ... ok\ntest_basic_math (tests.test_simple.SimpleTestCase.test_basic_math)\nTest basic math operations ... ok\ntest_dictionary_operations (tests.test_simple.SimpleTestCase.test_dictionary_operations)\nTest dictionary operations ... ok\ntest_list_operations (tests.test_simple.SimpleTestCase.test_list_operations)\nTest list operations ... ok\ntest_string_operations (tests.test_simple.SimpleTestCase.test_string_operations)\nTest string operations ... ok\n\n======================================================================\nERROR: test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)\nTest adding user to group\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 246, in test_add_user_to_group\n    reverse('add_user_to_group', kwargs={'group_id': self.test_group.id}),\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'add_user_to_group' not found. 'add_user_to_group' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)\nTest creating group\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 205, in test_create_group\n    response = self.post_json(reverse('create_group'), group_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)\nTest creating group with duplicate name\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 219, in test_create_group_duplicate_name\n    response = self.post_json(reverse('create_group'), group_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)\nTest getting group details\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 227, in test_get_group_details\n    reverse('get_group_details', kwargs={'group_id': self.test_group.id})\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'get_group_details' not found. 'get_group_details' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)\nTest group operations without admin permission\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 277, in test_group_permissions_unauthorized\n    response = self.post_json(reverse('create_group'), group_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)\nTest listing groups\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 185, in test_list_groups\n    response = self.client.get(reverse('list_groups'))\n                               ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_groups' not found. 'list_groups' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)\nTest removing user from group\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 263, in test_remove_user_from_group\n    reverse('remove_user_from_group', kwargs={'group_id': self.test_group.id}),\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'remove_user_from_group' not found. 'remove_user_from_group' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)\nTest admin permissions\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 304, in test_admin_permissions\n    response = self.client.get(reverse('list_users'))\n                               ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)\nTest user hierarchy permissions\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 329, in test_hierarchy_permissions\n    reverse('toggle_user_status', kwargs={'user_id': self.superuser.id})\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)\nTest regular user restrictions\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 318, in test_regular_user_restrictions\n    response = self.client.get(reverse('list_users'))\n                               ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)\nTest superuser has all permissions\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 293, in test_superuser_permissions\n    response = self.client.get(reverse('list_users'))\n                               ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)\nTest bulk activate users\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 148, in test_bulk_user_action_activate\n    response = self.post_json(reverse('bulk_user_action'), bulk_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)\nTest bulk delete users\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 168, in test_bulk_user_action_delete\n    response = self.post_json(reverse('bulk_user_action'), bulk_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)\nTest creating user as admin\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 47, in test_create_user_as_admin\n    response = self.post_json(reverse('create_user'), user_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)\nTest creating user with duplicate email\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 74, in test_create_user_duplicate_email\n    response = self.post_json(reverse('create_user'), user_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)\nTest creating user with invalid data\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 82, in test_create_user_invalid_data\n    response = self.post_json(reverse('create_user'), user_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)\nTest creating user and assigning to group\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 62, in test_create_user_with_group\n    response = self.post_json(reverse('create_user'), user_data)\n                              ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)\nTest getting user details\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 90, in test_get_user_details\n    reverse('get_user_details', kwargs={'user_id': self.regular_user.id})\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'get_user_details' not found. 'get_user_details' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)\nTest listing users as admin\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 20, in test_list_users_as_admin\n    response = self.client.get(reverse('list_users'))\n                               ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)\nTest listing users without admin permission\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 39, in test_list_users_unauthorized\n    response = self.client.get(reverse('list_users'))\n                               ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)\nTest toggling user status\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 127, in test_toggle_user_status\n    reverse('toggle_user_status', kwargs={'user_id': self.regular_user.id})\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)\nTest updating user\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 110, in test_update_user\n    reverse('update_user', kwargs={'user_id': self.regular_user.id}),\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'update_user' not found. 'update_user' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)\nTest that new captcha replaces old one\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 35, in test_captcha_regeneration\n    response1 = self.client.post(reverse('generate_captcha'))\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)\nTest that captcha answer is stored in session\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 27, in test_captcha_stored_in_session\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)\nTest captcha generation\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 18, in test_generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)\nTest login with inactive user\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 111, in test_inactive_user_login\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)\nTest login with invalid captcha\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 92, in test_invalid_captcha\n    self.generate_captcha()  # Generate captcha but use wrong answer\n    ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)\nTest login with invalid credentials\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 76, in test_invalid_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)\nTest login with missing required fields\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 133, in test_missing_fields\n    response = self.post_json(reverse('login'), {})\n                              ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'login' not found. 'login' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)\nTest successful BCE login\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 51, in test_successful_bce_login\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)\nTest successful vendor login\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 64, in test_successful_vendor_login\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)\nTest that user permissions are included in login response\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 140, in test_user_permissions_in_response\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)\nTest BCE user trying to login via vendor\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 122, in test_wrong_login_type\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)\nTest logout without being logged in\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 170, in test_logout_without_login\n    response = self.client.post(reverse('logout'))\n                                ^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)\nTest successful logout\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 160, in test_successful_logout\n    response = self.client.post(reverse('logout'))\n                                ^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)\nTest that session is cleaned up on logout\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 193, in test_session_cleanup_on_logout\n    response = self.client.post(reverse('logout'))\n                                ^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.\n\n======================================================================\nERROR: test_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)\nTest user type validation\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 105, in _execute\n    return self.cursor.execute(sql, params)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\psycopg\\cursor.py\", line 97, in execute\n    raise ex.with_traceback(None)\npsycopg.errors.UniqueViolation: duplicate key value violates unique constraint \"accounts_user_username_key\"\nDETAIL:  Key (username)=(<EMAIL>) already exists.\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_models.py\", line 129, in test_user_type_choices\n    user = User.objects.create_user(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\accounts\\models.py\", line 16, in create_user\n    user.save(using=self._db)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\contrib\\auth\\base_user.py\", line 65, in save\n    super().save(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\base.py\", line 902, in save\n    self.save_base(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\base.py\", line 1008, in save_base\n    updated = self._save_table(\n              ^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\base.py\", line 1169, in _save_table\n    results = self._do_insert(\n              ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\base.py\", line 1210, in _do_insert\n    return manager._insert(\n           ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\manager.py\", line 87, in manager_method\n    return getattr(self.get_queryset(), name)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\query.py\", line 1868, in _insert\n    return query.get_compiler(using=using).execute_sql(returning_fields)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\models\\sql\\compiler.py\", line 1882, in execute_sql\n    cursor.execute(sql, params)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 79, in execute\n    return self._execute_with_wrappers(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 92, in _execute_with_wrappers\n    return executor(sql, params, many, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 100, in _execute\n    with self.db.wrap_database_errors:\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\utils.py\", line 91, in __exit__\n    raise dj_exc_value.with_traceback(traceback) from exc_value\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 105, in _execute\n    return self.cursor.execute(sql, params)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\psycopg\\cursor.py\", line 97, in execute\n    raise ex.with_traceback(None)\ndjango.db.utils.IntegrityError: duplicate key value violates unique constraint \"accounts_user_username_key\"\nDETAIL:  Key (username)=(<EMAIL>) already exists.\n\n======================================================================\nFAIL: test_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)\nTest user permission methods\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 226, in test_user_permissions\n    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))\nAssertionError: False is not true\n\n======================================================================\nFAIL: test_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)\nTest user string representation\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 240, in test_user_string_representation\n    self.assertEqual(str(self.admin_user), '<EMAIL>')\nAssertionError: '<EMAIL> (Admin)' != '<EMAIL>'\n- <EMAIL> (Admin)\n?               --------\n+ <EMAIL>\n\n\n======================================================================\nFAIL: test_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)\nTest full_name property\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_models.py\", line 87, in test_full_name_property\n    self.assertEqual(user.full_name, '<EMAIL>')\nAssertionError: '' != '<EMAIL>'\n+ <EMAIL>\n\n\n======================================================================\nFAIL: test_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)\nTest user permission hierarchy\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_models.py\", line 92, in test_user_permissions_hierarchy\n    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))\nAssertionError: False is not true\n\n======================================================================\nFAIL: test_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)\nTest user __str__ method\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_models.py\", line 56, in test_user_string_representation\n    self.assertEqual(str(self.admin_user), '<EMAIL>')\nAssertionError: '<EMAIL> (Admin)' != '<EMAIL>'\n- <EMAIL> (Admin)\n?               --------\n+ <EMAIL>\n\n\n----------------------------------------------------------------------\nRan 61 tests in 65.759s\n\nFAILED (failures=5, errors=37)\nPreserving test database for alias 'default' ('test_th')...\n\n"}, "summary": {"overall_status": "failed", "frontend_status": "failed", "backend_status": "failed", "timestamp": "2025-07-15T09:09:26.133931"}}