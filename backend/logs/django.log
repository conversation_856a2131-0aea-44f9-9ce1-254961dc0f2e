Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2628
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2679
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: SA001 (<EMAIL>)
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Captcha generated: 13 - 17 = -4
Captcha generated: 17 * 17 = 289
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 19 + 20 = 39
Captcha generated: 12 + 17 = 29
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 13 + 10 = 23
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 19 + 9 = 28
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 14 + 5 = 19
Captcha generated: 9 * 3 = 27
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login attempt with non-existent employee ID: <EMAIL>
Unauthorized: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 401 47
Captcha generated: 9 + 13 = 22
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 18 + 20 = 38
Captcha generated: 5 + 6 = 11
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
Captcha generated: 9 - 12 = -3
Captcha generated: 12 * 1 = 12
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login attempt with non-existent employee ID: <EMAIL>
Unauthorized: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 401 47
Captcha generated: 4 + 11 = 15
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
BCE login successful: SA001 (<EMAIL>)
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/e9404a5d-e423-4489-b268-dec55a76154d/toggle-status/
"POST /api/admin-controls/users/e9404a5d-e423-4489-b268-dec55a76154d/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/4dd0f8be-0772-49fe-b10d-a276853a790f/toggle-status/
"POST /api/admin-controls/users/4dd0f8be-0772-49fe-b10d-a276853a790f/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/4fbb801a-8d99-4885-b0bd-09a7cb82fba8/toggle-status/
"POST /api/admin-controls/users/4fbb801a-8d99-4885-b0bd-09a7cb82fba8/toggle-status/ HTTP/1.1" 403 45
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 7 + 18 = 25 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 11 + 6 = 17 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 18 + 20 = 38 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 14 + 19 = 33 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 18 + 15 = 33 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 8 * 17 = 136 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 10 + 17 = 27 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
"OPTIONS /api/auth/vendor-login/ HTTP/1.1" 200 0
Error in vendor login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/vendor-login/
"POST /api/auth/vendor-login/ HTTP/1.1" 500 40
Captcha generated: 14 + 10 = 24 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 3 - 18 = -15 (Session: jxcc3yyk6gqg2c75kr8735gvugxzbqb8)
Captcha generated: 9 + 1 = 10 (Session: chqf2oq2xgt635cep45dk1t2gre4g98n)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 10 * 11 = 110 (Session: kb4y04b5nswhzh9lbttz2ev0ls6k3n85)
Captcha generated: 5 + 9 = 14 (Session: iivzovbf7iv299wxogbc71fn1vfelwiy)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
Failed BCE login attempt: <EMAIL>
Unauthorized: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 401 47
Captcha generated: 19 * 18 = 342 (Session: iivzovbf7iv299wxogbc71fn1vfelwiy)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Bad Request: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 400 50
Captcha generated: 7 * 11 = 77 (Session: iivzovbf7iv299wxogbc71fn1vfelwiy)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Bad Request: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 400 50
Captcha generated: 7 - 9 = -2 (Session: iivzovbf7iv299wxogbc71fn1vfelwiy)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
Captcha generated: 7 + 7 = 14 (Session: iivzovbf7iv299wxogbc71fn1vfelwiy)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 7 - 5 = 2 (Session: cqi7jvw80hjgnmpjs22sk4i1ffh5nzpo)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
Captcha generated: 7 + 3 = 10 (Session: m8ippxu2x0kvrho1bnjl6x02h8hqhir8)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 8 - 17 = -9 (Session: 5vthlux6lvdz8ouubdikc9fj6nmkakbm)
Captcha generated: 2 - 15 = -13 (Session: oki6a0lv8asbpvnmvjh0uqlh9v3xbzoc)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 5 * 6 = 30 (Session: oki6a0lv8asbpvnmvjh0uqlh9v3xbzoc)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
Captcha generated: 11 * 3 = 33 (Session: oki6a0lv8asbpvnmvjh0uqlh9v3xbzoc)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"OPTIONS /api/auth/vendor-login/ HTTP/1.1" 200 0
Bad Request: /api/auth/vendor-login/
"POST /api/auth/vendor-login/ HTTP/1.1" 400 50
Captcha generated: 11 - 8 = 3 (Session: oki6a0lv8asbpvnmvjh0uqlh9v3xbzoc)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Vendor login successful: <EMAIL>
"POST /api/auth/vendor-login/ HTTP/1.1" 200 145
"GET /api/core/dashboard/ HTTP/1.1" 200 483
"GET /api/core/dashboard/ HTTP/1.1" 200 483
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 19 * 18 = 342 (Session: u5b8c3ogxsw424osiianqa9mfcas2bib)
Captcha generated: 9 - 2 = 7 (Session: fhefazqfqdgh0r3dcwh7sa0vxum0mngl)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 221
"GET /api/core/dashboard/ HTTP/1.1" 200 479
"GET /api/core/dashboard/ HTTP/1.1" 200 479
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1479
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1479
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 18 + 11 = 29 (Session: i7aitdyohnl457ku9kf8pn9ya84oasg7)
Captcha generated: 16 + 13 = 29 (Session: s6wn5uvetjn9p71h30cpfapw5g9vq80i)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 222
"GET /api/core/dashboard/ HTTP/1.1" 200 493
"GET /api/core/dashboard/ HTTP/1.1" 200 493
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 12 - 8 = 4 (Session: lg25evg9pkk4o0fc3u2vkgiy2p8k3h7p)
Captcha generated: 18 * 11 = 198 (Session: 0lel9jc2pui1azislts7hd7a6j8xzgr7)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
Bad Request: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 400 50
Captcha generated: 10 * 6 = 60 (Session: 0lel9jc2pui1azislts7hd7a6j8xzgr7)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10&search=r HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10&search=re HTTP/1.1" 200 507
"GET /api/admin-controls/users/?page=1&per_page=10&search=r HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10&user_type=superadmin HTTP/1.1" 200 508
"GET /api/admin-controls/users/?page=1&per_page=10&user_type=admin HTTP/1.1" 200 494
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10&is_active=false HTTP/1.1" 200 138
"GET /api/admin-controls/users/?page=1&per_page=10&is_active=true HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\urls.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 15 + 7 = 22 (Session: nnusiqoxntovn9dw8p2bfz4r8gdg839o)
Captcha generated: 16 + 1 = 17 (Session: 66hwthf3izkwwsqz4aleu8o180s5fbun)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 14 + 15 = 29 (Session: 66hwthf3izkwwsqz4aleu8o180s5fbun)
Captcha generated: 7 * 15 = 105 (Session: 66hwthf3izkwwsqz4aleu8o180s5fbun)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 17 * 8 = 136 (Session: 66hwthf3izkwwsqz4aleu8o180s5fbun)
Captcha generated: 5 * 16 = 80 (Session: 66hwthf3izkwwsqz4aleu8o180s5fbun)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Captcha generated: 11 + 5 = 16 (Session: kft26dyx5aym54pldjkuqy6fca3g3kkp)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 10 * 13 = 130 (Session: wqlr2ma883irkg55vfv9a5x15nrjxltf)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: <EMAIL>
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1880
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\admin_controls\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
