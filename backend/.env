# Database Configuration
POSTGRES_DB=th
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

MONGODB_DB=th
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/

# Django Configuration
SECRET_KEY=django-insecure-your-secret-key-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Application Configuration
APP_HOST=localhost
APP_PORT=8000
FRONTEND_URL=http://localhost:5173

# Security
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:5174
CSRF_TRUSTED_ORIGINS=http://localhost:5173,http://localhost:5174

# Session Configuration
SESSION_COOKIE_AGE=86400
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
