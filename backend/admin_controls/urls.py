from django.urls import path
from . import views

app_name = 'admin_controls'

urlpatterns = [
    # User management
    path('users/', views.list_users, name='list_users'),
    path('users/create/', views.create_user, name='create_user'),
    path('users/<uuid:user_id>/', views.get_user_details, name='get_user_details'),
    path('users/<uuid:user_id>/update/', views.update_user, name='update_user'),
    path('users/<uuid:user_id>/toggle-status/', views.toggle_user_status, name='toggle_user_status'),

    # Group management
    path('groups/', views.list_groups, name='list_groups'),
    path('groups/create/', views.create_group, name='create_group'),
]
