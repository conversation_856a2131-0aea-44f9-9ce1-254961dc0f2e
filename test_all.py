#!/usr/bin/env python3
"""
Windows-compatible test runner for Talent Hero application
"""
import os
import sys
import subprocess
import json
import datetime
from pathlib import Path
import shutil


class WindowsTestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results_dir = self.project_root / 'test-results'
        self.test_results_dir.mkdir(exist_ok=True)
        
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'frontend': {},
            'backend': {},
            'e2e': {},
            'summary': {}
        }
    
    def check_npm_available(self):
        """Check if npm is available"""
        npm_commands = ['npm', 'npm.cmd']
        
        for npm_cmd in npm_commands:
            if shutil.which(npm_cmd):
                return npm_cmd
        
        return None
    
    def run_frontend_tests(self):
        """Run frontend tests with Vitest"""
        print("🧪 Running Frontend Tests...")
        print("=" * 50)
        
        npm_cmd = self.check_npm_available()
        if not npm_cmd:
            print("❌ npm not found. Please ensure Node.js and npm are installed and in PATH.")
            self.results['frontend'] = {
                'status': 'error',
                'error': 'npm not found in PATH'
            }
            return
        
        try:
            print(f"Using npm command: {npm_cmd}")
            
            # First, try to run a simple npm command to verify it works
            test_result = subprocess.run([
                npm_cmd, '--version'
            ], 
            cwd=str(self.project_root),
            capture_output=True, 
            text=True,
            timeout=30,
            shell=True
            )
            
            if test_result.returncode != 0:
                print(f"❌ npm test failed: {test_result.stderr}")
                self.results['frontend'] = {
                    'status': 'error',
                    'error': f'npm test failed: {test_result.stderr}'
                }
                return
            
            print(f"✅ npm version: {test_result.stdout.strip()}")
            
            # Run the actual tests
            result = subprocess.run([
                npm_cmd, 'run', 'test:run'
            ],
            cwd=str(self.project_root),
            capture_output=True,
            text=True,
            timeout=300,
            shell=True,
            encoding='utf-8',
            errors='replace'  # Replace problematic characters
            )
            
            self.results['frontend'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            print(f"✅ Frontend tests completed with return code: {result.returncode}")
            if result.stdout:
                print("Frontend output:")
                print(result.stdout[-1000:])  # Show last 1000 chars
            
        except subprocess.TimeoutExpired:
            print("❌ Frontend tests timed out")
            self.results['frontend'] = {
                'status': 'timeout',
                'error': 'Tests timed out after 300 seconds'
            }
        except Exception as e:
            print(f"❌ Error running frontend tests: {e}")
            self.results['frontend'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_backend_tests(self):
        """Run Django backend tests"""
        print("\n🧪 Running Backend Tests...")
        print("=" * 50)
        
        try:
            backend_dir = self.project_root / 'backend'
            
            # Check if manage.py exists
            manage_py = backend_dir / 'manage.py'
            if not manage_py.exists():
                print(f"❌ manage.py not found at {manage_py}")
                self.results['backend'] = {
                    'status': 'error',
                    'error': 'manage.py not found'
                }
                return
            
            print(f"Using Python: {sys.executable}")
            print(f"Backend directory: {backend_dir}")
            
            # Run Django tests
            result = subprocess.run([
                sys.executable, 'manage.py', 'test',
                '--verbosity=2',
                '--keepdb'
            ],
            cwd=str(backend_dir),
            capture_output=True,
            text=True,
            timeout=300,
            shell=True,
            encoding='utf-8',
            errors='replace'  # Replace problematic characters
            )
            
            self.results['backend'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            print(f"✅ Backend tests completed with return code: {result.returncode}")
            if result.stdout:
                print("Backend output:")
                print(result.stdout[-1000:])  # Show last 1000 chars
            
        except subprocess.TimeoutExpired:
            print("❌ Backend tests timed out")
            self.results['backend'] = {
                'status': 'timeout',
                'error': 'Tests timed out after 300 seconds'
            }
        except Exception as e:
            print(f"❌ Error running backend tests: {e}")
            self.results['backend'] = {
                'status': 'error',
                'error': str(e)
            }

    def run_e2e_tests(self):
        """Run Playwright E2E tests"""
        print("\n🎭 Running E2E Tests...")
        print("=" * 50)

        npm_cmd = self.check_npm_available()
        if not npm_cmd:
            print("❌ npm not found. Skipping E2E tests.")
            self.results['e2e'] = {
                'status': 'skipped',
                'error': 'npm not found in PATH'
            }
            return

        try:
            print(f"Using npm command: {npm_cmd}")

            # Run Playwright tests
            result = subprocess.run([
                npm_cmd, 'run', 'test:e2e'
            ],
            cwd=str(self.project_root),
            capture_output=True,
            text=True,
            timeout=600,  # 10 minutes for E2E tests
            shell=True,
            encoding='utf-8',
            errors='replace'
            )

            self.results['e2e'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }

            print(f"✅ E2E tests completed with return code: {result.returncode}")
            if result.stdout:
                print("E2E output:")
                print(result.stdout[-1000:])  # Show last 1000 chars

        except subprocess.TimeoutExpired:
            print("❌ E2E tests timed out")
            self.results['e2e'] = {
                'status': 'timeout',
                'error': 'Tests timed out after 600 seconds'
            }
        except Exception as e:
            print(f"❌ Error running E2E tests: {e}")
            self.results['e2e'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_summary(self):
        """Generate test summary"""
        frontend_status = self.results['frontend'].get('status', 'unknown')
        backend_status = self.results['backend'].get('status', 'unknown')
        e2e_status = self.results['e2e'].get('status', 'unknown')

        overall_status = 'passed'
        if (frontend_status in ['failed', 'error', 'timeout'] or
            backend_status in ['failed', 'error', 'timeout'] or
            e2e_status in ['failed', 'error', 'timeout']):
            overall_status = 'failed'

        self.results['summary'] = {
            'overall_status': overall_status,
            'frontend_status': frontend_status,
            'backend_status': backend_status,
            'e2e_status': e2e_status,
            'timestamp': self.results['timestamp']
        }
    
    def save_results(self):
        """Save test results to file"""
        results_file = self.test_results_dir / f'test-report-{self.timestamp}.json'
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Also save as latest
        latest_file = self.test_results_dir / 'latest-test-report.json'
        with open(latest_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Test results saved to: {results_file}")
        print(f"📊 Latest results: {latest_file}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        
        summary = self.results['summary']
        
        # Overall status
        status_emoji = "✅" if summary['overall_status'] == 'passed' else "❌"
        print(f"{status_emoji} Overall Status: {summary['overall_status'].upper()}")
        
        # Frontend results
        frontend_emoji = "✅" if summary['frontend_status'] == 'passed' else "❌"
        print(f"{frontend_emoji} Frontend Tests: {summary['frontend_status'].upper()}")
        
        # Backend results
        backend_emoji = "✅" if summary['backend_status'] == 'passed' else "❌"
        print(f"{backend_emoji} Backend Tests: {summary['backend_status'].upper()}")

        # E2E results
        e2e_emoji = "✅" if summary['e2e_status'] == 'passed' else "❌"
        print(f"{e2e_emoji} E2E Tests: {summary['e2e_status'].upper()}")

        print(f"🕐 Timestamp: {summary['timestamp']}")
        print("=" * 60)
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Comprehensive Test Suite (Unit + Integration + E2E)")
        print("=" * 60)
        
        # Run tests
        self.run_frontend_tests()
        self.run_backend_tests()
        self.run_e2e_tests()
        
        # Generate summary and save results
        self.generate_summary()
        self.save_results()
        self.print_summary()
        
        # Generate HTML report
        try:
            print("\nGenerating HTML report...")
            subprocess.run([
                sys.executable, 'generate_test_report.py'
            ], cwd=str(self.project_root), timeout=30)
            print("📊 HTML report generated successfully")
        except Exception as e:
            print(f"❌ Error generating HTML report: {e}")
        
        # Return exit code based on overall status
        return 0 if self.results['summary']['overall_status'] == 'passed' else 1


def main():
    """Main entry point"""
    runner = WindowsTestRunner()
    exit_code = runner.run_all_tests()
    
    print(f"\n🎉 Test execution completed!")
    print(f"📊 Check test-results/ directory for detailed reports")
    print(f"📊 Open test-results/latest-test-report.html for visual report")
    
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
