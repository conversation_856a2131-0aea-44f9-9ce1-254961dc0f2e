# Talent Hero v3.11

A comprehensive talent management system built with Django REST Framework backend and React TypeScript frontend. Features role-based access control with superadmin/admin/user hierarchy, vendor management, and group-based permissions.

## Key Features

- **Multi-tier Authentication**: Vendor and BCE login paths with arithmetic captcha
- **User Management**: Create, edit, activate/deactivate users with bulk operations
- **Group Management**: Assign users to groups with permission controls
- **Admin Panel**: Comprehensive dashboard for system administration
- **Dark Mode UI**: Modern interface using shadcn components

## Tech Stack

**Backend**: Django 5.2.4, PostgreSQL, MongoDB, Django Guardian
**Frontend**: React, TypeScript, Vite, Tailwind CSS
**Authentication**: Session-based with CSRF protection

## Quick Start

Backend: `cd backend && python manage.py runserver`
Frontend: `npm run dev`

Access BCE panel at `/bce` for admin users, `/vendor` for vendor access.
