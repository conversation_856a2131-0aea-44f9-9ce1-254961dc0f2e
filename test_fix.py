#!/usr/bin/env python3
"""
Test the CSRF fix
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_endpoints():
    """Test all endpoints that were failing"""
    print("Testing endpoints after CSRF fix...")
    print("=" * 50)
    
    # Test captcha generation
    print("1. Testing captcha generation...")
    response = requests.post(f"{BASE_URL}/auth/generate-captcha/")
    print(f"   Status: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
    
    # Test logout
    print("2. Testing logout...")
    response = requests.post(f"{BASE_URL}/auth/logout/")
    print(f"   Status: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
    
    # Test user toggle (should be 404 for non-existent user, not 403 CSRF)
    print("3. Testing user toggle...")
    response = requests.post(f"{BASE_URL}/admin-controls/users/test-id/toggle-status/")
    print(f"   Status: {response.status_code} - {'✅ PASS (404/403 expected)' if response.status_code in [404, 403] else '❌ FAIL'}")
    
    # Test user creation (should be 403 permission denied, not CSRF)
    print("4. Testing user creation...")
    response = requests.post(f"{BASE_URL}/admin-controls/users/create/", 
                           json={"email": "<EMAIL>", "password": "test123"})
    print(f"   Status: {response.status_code} - {'✅ PASS (403 expected)' if response.status_code == 403 else '❌ FAIL'}")
    
    # Test group creation (should be 403 permission denied, not CSRF)
    print("5. Testing group creation...")
    response = requests.post(f"{BASE_URL}/admin-controls/groups/create/", 
                           json={"name": "test-group"})
    print(f"   Status: {response.status_code} - {'✅ PASS (403 expected)' if response.status_code == 403 else '❌ FAIL'}")
    
    # Test bulk action (should be 403 permission denied, not CSRF)
    print("6. Testing bulk action...")
    response = requests.post(f"{BASE_URL}/admin-controls/users/bulk-action/", 
                           json={"user_ids": ["test-id"], "action": "activate"})
    print(f"   Status: {response.status_code} - {'✅ PASS (403 expected)' if response.status_code == 403 else '❌ FAIL'}")
    
    print("=" * 50)
    print("✅ All endpoints should now work properly when authenticated!")
    print("The 403 errors above are expected since we're not logged in.")
    print("When you're logged in through the frontend, these should work.")

if __name__ == "__main__":
    test_endpoints()
