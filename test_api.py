#!/usr/bin/env python3
"""
Simple test script to verify API endpoints are working
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_captcha_generation():
    """Test captcha generation"""
    print("Testing captcha generation...")
    response = requests.post(f"{BASE_URL}/auth/generate-captcha/")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Success: {data.get('success')}")
        print(f"Question: {data.get('question')}")
    else:
        print(f"Error: {response.text}")
    print("-" * 50)

def test_logout():
    """Test logout endpoint"""
    print("Testing logout...")
    response = requests.post(f"{BASE_URL}/auth/logout/")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Success: {data.get('success')}")
        print(f"Message: {data.get('message')}")
    else:
        print(f"Error: {response.text}")
    print("-" * 50)

def test_admin_endpoints():
    """Test admin endpoints (will fail without authentication, but should not be CSRF 403)"""
    print("Testing admin endpoints (without auth - should get 401/403 permission denied, not CSRF 403)...")
    
    # Test user toggle
    response = requests.post(f"{BASE_URL}/admin-controls/users/test-id/toggle-status/")
    print(f"Toggle user status: {response.status_code}")
    
    # Test user creation
    response = requests.post(f"{BASE_URL}/admin-controls/users/create/", 
                           json={"email": "<EMAIL>", "password": "test123"})
    print(f"Create user status: {response.status_code}")
    
    # Test group creation
    response = requests.post(f"{BASE_URL}/admin-controls/groups/create/", 
                           json={"name": "test-group"})
    print(f"Create group status: {response.status_code}")
    
    # Test bulk action
    response = requests.post(f"{BASE_URL}/admin-controls/users/bulk-action/", 
                           json={"user_ids": ["test-id"], "action": "activate"})
    print(f"Bulk action status: {response.status_code}")
    
    print("-" * 50)

if __name__ == "__main__":
    print("Testing API endpoints...")
    print("=" * 50)
    
    test_captcha_generation()
    test_logout()
    test_admin_endpoints()
    
    print("Test completed!")
